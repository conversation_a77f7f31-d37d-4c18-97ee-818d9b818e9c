# Order Management Hooks

This module provides React Query mutation hooks for order management operations.

## Available Hooks

### `useAssignOrderToDriver`

A mutation hook for assigning orders to drivers.

#### Usage

```typescript
import { useAssignOrderToDriver } from '@/api/orderManagment/useOrderManagement';

const MyComponent = () => {
  const assignOrderMutation = useAssignOrderToDriver({
    onSuccess: (data) => {
      console.log('Order assigned successfully:', data);
      // Additional success handling
    },
    onError: (error) => {
      console.error('Failed to assign order:', error);
      // Additional error handling
    },
  });

  const handleAssignOrder = (orderId: string, driverId: string) => {
    assignOrderMutation.mutate({
      orderId,
      driverId,
    });
  };

  return (
    <button
      onClick={() => handleAssignOrder('order-123', 'driver-456')}
      disabled={assignOrderMutation.isPending}
    >
      {assignOrderMutation.isPending ? 'Assigning...' : 'Assign Order'}
    </button>
  );
};
```

#### Features

- **Automatic notifications**: Shows success/error notifications using the app's notification system
- **Query invalidation**: Automatically invalidates related queries (Orders, Order_Management) on success
- **TypeScript support**: Fully typed with proper error handling
- **Customizable**: Accepts custom `onSuccess` and `onError` callbacks

#### Parameters

- `orderId` (string): The ID of the order to assign
- `driverId` (string): The ID of the driver to assign the order to

#### Returns

Standard React Query mutation object with:
- `mutate`: Function to trigger the mutation
- `isPending`: Boolean indicating if the mutation is in progress
- `isError`: Boolean indicating if the mutation failed
- `isSuccess`: Boolean indicating if the mutation succeeded
- `error`: Error object if the mutation failed
- `data`: Response data if the mutation succeeded
