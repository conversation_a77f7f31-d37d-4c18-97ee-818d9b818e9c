import { apiClient } from "..";
import { AUTH_ENDPOINTS } from "../endpoints/AuthEndpoints";

const assignOrderToDriver = async () => {
    const response = await api.post(AUTH_ENDPOINTS.LOGIN, payload);
    
    if (response.status === 200 && response.data) {
      setStringifyStorageItem(StorageKeys.USER_INFO, { ...response.data.user, role: 'Tenant' }); //TODO: remove static role after role based access implementation.
      setStorageItem(StorageKeys.IS_AUTHENTICATED, 'true');
      return response.data as { user: IUser };
    }

}

const api = apiClient.getAxiosInstance();

export const login = async (payload: ILogin): Promise<{ user: IUser } | void> => {
};