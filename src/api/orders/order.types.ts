export type IOrserStatus =
    | 'Draft'
    | 'Submitted'
    | 'Pending'
    | 'Assigned'
    | 'In_Progress'
    | 'Completed'
    | 'Cancelled';
export interface CreateOrderItemDto {
    packageTemplateId?: string;
    itemType: string;
    quantity: number;
    weight?: number;
    weightUnit?: string;
    length?: number;
    width?: number;
    height?: number;
    dimensionUnit?: string;
    declaredValue?: number;
    description?: string;
    notes?: string;
    imageUrl?: string;
    metadata?: {
        [key: string]: any;
    };
}

export interface ResponseOrderItemDto extends CreateOrderItemDto {
    id: string;
    orderId: string;
    createdAt: string;
    updatedAt: string;
    packageTemplateName?: string;
}

export interface IOrderItemPaginatedResponse {
    data: ResponseOrderItemDto[];
    total: number;
    skip: number;
    limit: number;
}

export interface CreateOrdersDto {
    referenceNumber?: string;
    status?: string;
    customerId: string;
    requestedById: string;
    submittedById: string;
    packageTemplateId?: string;
    collectionAddressId: string;
    collectionContactName?: string;
    collectionInstructions?: string;
    collectionSignatureRequired?: boolean;
    scheduledCollectionTime?: string;
    collectionZoneId?: string;
    deliveryAddressId: string;
    deliveryContactName?: string;
    deliveryInstructions?: string;
    deliverySignatureRequired?: boolean;
    scheduledDeliveryTime?: string;
    deliveryZoneId?: string;
    totalItems?: number;
    totalVolume?: number;
    totalWeight?: number;
    declaredValue?: number;
    vehicleTypeId?: string;
    assignedDriverId?: string;
    assignedVehicleId?: string;
    codAmount?: number;
    priceSetId?: string;
    basePriceType?: string;
    distance?: number;
    distanceUnit?: string;
    description?: string;
    comments?: string;
    internalNotes?: string;
    items?: CreateOrderItemDto[];
    serviceLevel?: string;
    customFields?: {
        [key: string]: any;
    };
    metadata?: {
        [key: string]: any;
    };
}

export interface IResponseOrderDto extends CreateOrdersDto {
    id: string;
    orderId: string;
    createdAt: string;
    updatedAt: string;
}

export interface IOrderPaginatedResponse {
    data: IResponseOrderDto[];
    total: number;
    skip: number;
    limit: number;
}
