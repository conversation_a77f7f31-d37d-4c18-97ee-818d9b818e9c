const EN = {
  common: {
    login: 'Login',
    logout: 'Logout',
    dashboard: 'Dashboard',
    profile: 'Profile',
    deliveries: 'Deliveries',
    tracking: 'Tracking',
    reports: 'Reports',
    duplicate: 'Yes, duplicate',
    allow: 'Allow',
    deny: 'Deny',
    doItAnyWay: 'Do it anyway',
    notUpdatedYet: 'Not updated yet',
    noMatchesFound: 'No matches found',
    somethingWrong: 'Something went wrong',
    usa: 'USA +1',
    can: 'CAN +1',
    ind: 'IND +1',
    errors: {
      noMultipleWhiteSpace: 'No multiple white spaces allowed',
      noSpacialCharacters: 'No special characters are allowed',
      noWhiteSpace: 'No white spaces allowed',
      valueMustBeMoreThan1: 'Value must be more than 1',
    },
    error: 'Error',
    success: 'Success',
    warn: 'Warning',
    update: 'Update',
    save: 'Save',
    leave: 'Leave',
    stay: 'Stay',
    cancel: 'Cancel',
    divider: {
      basicDetails: 'Basic details',
    },
    print: 'Print',
    delete: 'Delete',
    added: 'Added',
    add: 'Add',
    modified: 'Modified',
    lastUpdatedBy: 'Last updated By',
    discardChanges: 'Discard changes',
    alert: {
      areYouSure: 'Are you sure you want to leave?',
      preventExist: 'Unsaved changes will be lost.',
      linkSent: 'Link has been sent to {{email}}',
      resendLinkSent: 'The resend link has been sent to {{email}}',
    },
  },
  auth: {
    email: 'Email',
    password: 'Password',
    loginButton: 'Login',
    loginTitle: 'Login to {{appName}}',
    loginSubtitle: 'Welcome back! Please enter your details',
    emailRequired: 'Email is required',
    invalidEmail: 'Please enter a valid email address',
    passwordRequired: 'Password is required',
    passwordTooShort: 'Password must be at least {{length}} characters',
    errorTitle: 'Login Failed',
    invalidCredentials: 'Invalid email or password',
    successTitle: 'Success',
    loginSuccessful: 'Welcome back!',
    genericError: 'An error occurred. Please try again.',
    authenticating: 'Authenticating...',
    forgotPassword: 'Forgot password?',
    orDivider: 'or',
    noAccount: "Don't have an account?",
    signUpLink: 'Sign up.',
    remainingAttempts: '{{count}} attempt remaining before account is temporarily locked',
    logoutConfirmation: 'Are you sure you want to logout?',
    redirectTxt: 'You will be redirected to the login page',
    form: {
      email: {
        label: 'Email',
        placeholder: 'Enter your email',
      },
      password: {
        label: 'Password',
        placeholder: 'Enter your Password',
        notContainWhiteSpace: 'Password should not contain whitespace',
        invalidPassword: 'Please enter valid password',
      },
    },
    footer: {
      terms: 'Terms',
      privacy: 'Privacy',
      docs: 'Docs',
      helps: 'Helps',
    },
    forgetPasswordPage: {
      header: 'Forgot Password?',
      headerDescription:
        'Don’t worry! It’s happens. Please enter the email address associated with your account.',
      sendOtp: 'Send OTP',
    },
    otpVerificationPage: {
      header: 'Verify OTP',
      headerDescription: ' Please enter the {{length}} digit code sent to your email id',
      sendOtp: 'Send OTP',
      mustBeInLength: 'OTP must be {{length}} digits',
      resendOtp: 'Resend OTP',
      requireOtp: 'Please enter OTP',
      doNotGetOtp: "Didn't get the OTP?",
      verifyButton: 'Verify',
    },
    resetPasswordPage: {
      header: 'Reset password',
      headerDescription: ' Please enter the {{length}} digit code sent to your email id',
      resetPasswordButton: 'Reset password',
      requiredPassword: 'Please enter a new password',
      createPasswordPlaceholder: 'Create new password',
      confirmPasswordPlaceholder: 'Confirm new password',
      requiredConfirmPassword: 'Please confirm your password',
      doNotMatchPassword: 'Passwords do not match',
      minChar: 'At least 8 characters',
      numberOrSymbol: 'At least one number (0-9) or symbol',
      upperLowerCase: 'Lowercase (a-z) and uppercase (A-Z)',
      passwordStatus: {
        weak: 'Weak',
        medium: 'Medium',
        strong: 'Strong',
      },
    },
  },
  languages: {
    EN: 'English',
    FR: 'French',
  },
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome, {{name}}!',
    languageStatus: 'Current language: {{language}}',
    pendingDeliveries: 'Pending Deliveries',
    inTransit: 'In Transit',
    deliveredToday: 'Delivered Today',
    addDelivery: 'Add Delivery',
    changeUser: 'Change User',
    showWarning: 'Show Warning',
    translationDemo: 'Translation Demo',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',

    demoText:
      'Hello {{name}}! You have {{count}} pending deliveries. {{isVIP ? "You are a VIP customer." : "Consider upgrading to VIP."}}',
    newCustomer: 'New Customer',
    duplicateCustomer: 'Duplicate Customer',
    customer: {
      confirmDeleteCustomer: 'Are you sure you want to delete {{companyName}}?',
      customerDuplicatedSuccessfully: 'Customer duplicated successfully',
      failedToDuplicateCustomer: 'Failed to duplicate customer',
      newCustomer: 'New Customer',
      duplicateCustomer: 'Duplicate Customer',
      emailLoginDetails: 'Email Login Details',
      searchCustomer: 'Search Customer',
      addCustomer: 'Add Customer',
      seedCustomers: 'Seed Customers',
      failedToLoadCustomers: 'Failed to load customers',
      customerDeletedSuccessfully: 'Customer deleted successfully',
      failedToDeleteCustomer: 'Failed to delete customer',
      allowsInvoiceAccess: 'Allows contact to see the invoices and pay using supported methods',
      allowsPriceAccess: 'Allows contact to see prices of orders',
      allowsAddressAccess: 'Allows contact to access the list of addresses',
      selectOrAddDepartment: 'Select or add department',
      contactCreatedSuccessfully: 'Contact created successfully',
      contactUpdatedSuccessfully: 'Contact updated successfully',
      contactEmailExists: 'Contact with this email already exists',

      columns: {
        companyName: 'Company Name',
        accountNumber: 'Account Number',
        contactName: 'Customer Name',
        addressLine1: 'Address Line 1',
        city: 'City',
        phone: 'Phone',
        email: 'Email',
        fax: 'Fax',
        status: 'Status',
        category: 'Category',
        dateAdded: 'Date Added',
        dateUpdated: 'Date Updated',
        lastUpdateBy: 'Last Update By',
        action: 'Action',
        department: 'Department',
        selectAppropriateDepartment: 'Select the appropriate department',
        selectOrAddDepartment: 'Select or add department',
        contactActive: 'Contact is active',
        permissionSetting: 'Permission Setting',
        permissionSettingDescription:
          'Control what information and actions this contact can access.',
        invoices: 'Invoices',
        selectAppropriateCategory: 'Select the appropriate category',
        permission: 'Permissions',
        contactDeletedSuccessfully: 'Contact deleted successfully',
        deleteContact: 'Are you sure you want to delete this contact?',
        confirmDeleteContact:
          'This action cannot be undone. Deleting this contact will remove all their details and history from your records.',
        active: 'Active',
        inactive: 'Inactive',
        invoice: 'Invoice',
        failedToLoadContacts: 'Failed to load Contacts',
        enterNewContactDetails: 'Enter the details of the new contact',
        searchContact: 'Search Contact',
        updateContact: 'Update Contact',
        addContact: 'Add Contact',
        sendCredentials: 'Send Credentials',
        pleaseEnterYourName: 'Please enter your name',
        errorFetchingCountry: 'Error fetching country:',
        okay: 'Okay',
        customerEnabledSuccessfully: 'Customer activated successfully',
        customerDisabledSuccessfully: 'Customer deactivated successfully',
        confirmEnableCustomer: 'Are you sure you want to enable this customer?',
        confirmDisableCustomer: 'Are you sure you want to disable this customer?',
        enableCustomerDescription:
          'Activating this customer will enable their access to the portal and services.',
        disableCustomerDescription:
          'Deactivating this customer will revoke their access to the portal and services, but you will still be able to access their orders and financial information.',
        customerUpdatedSuccessfully: 'Customer updated successfully',
        failedToCreateCustomer: 'Failed to create customer',
        customerCreatedSuccessfully: 'Customer created successfully',
        pleaseEnterAccountNumber: 'Please enter your account number',
        pleaseEnterValidURL: 'Please enter a valid URL',
        selectOrAddCategory: 'Select or add category',
        addressDetails: 'Address details',
        faxNumber: 'Fax number',
        website: 'Website',
        customerIsActive: 'Customer status',
        pleaseEnterYourAddress: 'Please enter your address',
        pleaseEnterYourCity: 'Please enter your city',
        pleaseEnterYourProvince: 'Please enter your province',
        pleaseEnterYourPostalCode: 'Please enter your postal code',
        pleaseEnterYourCountry: 'Please enter your country',
        toggleCustomerStatus: 'Use this toggle to enable or disable a customer:',
        enabled: 'Enabled',
        enabledDescription: 'The customer can log in, place orders, and receive updates.',
        disabled: 'Disabled',
        disabledDescription:
          'The customer is restricted from access but their data remains for records.',
        activeOrdersNote: "Active orders won't be affected.",
        activate: 'Activate',
        deactivate: 'Deactivate',
        resendCredentials: 'Resend Credentials',
        formFieldNames: {
          companyName: 'Company name',
          accountNumber: 'Account number',
          contactName: 'Contact name',
          addressLine1: 'Address line 1',
          addressLine2: 'Address line 2',
          dateAdded: 'Date added',
          dateUpdated: 'Date updated',
          lastUpdateBy: 'Last update by',
          contactActive: 'Contact is active',
          permissionSetting: 'Permission setting',
          deleteContact: 'Delete contact',
        },
      },
      contactsEmptyState: {
        title: 'No contact Found',
        description: 'To get started',
        link: 'Add new contact',
      },
      settings: {
        notification: 'Notification',
        uiConfiguration: 'UI Configuration',
        field: 'Field',
        required: 'Required',
        visible: 'Visible',
        general: {
          generalSettings: 'General Settings',
          commonSettingsDescription:
            'All common settings you can customize your preferences across different channels',
          prepaidOrders: 'Pre-paid Orders',
          saveSettings: 'Save Settings',
          prepaidOrdersInfo:
            'Prepaid orders mean that payment is required before completing the order placement. If this option is not selected, the customer will be billed using the default automated monthly billing method.',
          generalSettingsUpdated: 'General settings updated successfully',
        },
        settingsNotification: {
          sms: 'SMS',
          email: 'Email',
          push: 'Push',
          inApp: 'In-app',
          notificationSettings: 'Notification Settings',
          notificationSettingsDescription:
            'Customize your notification preferences across different channels.',
          disableAll: 'Disable All',
          enableAll: 'Enable All',
          saveSettings: 'Save Settings',
          formField: 'UI configuration settings',
          formFieldDescription:
            'Enables customization of the order entry interface in the customer portal. It provides a flexible way to tailor the user experience by configuring various UI controls.',
          delivery: 'Delivery',
          collection: 'Collection',
          name: 'Name',
          required: 'Required',
          visible: 'Visible',
          packagingType: 'Packaging Type',
          weight: 'Weight',
          width: 'Width',
          height: 'Height',
          length: 'Length',
          maxWeight: 'Max Weight',
          editPackageTypeInfo: 'Edit Package Type Info',
          enterNewPackageTypeDetails: 'Enter the details of the new package type below',
          basicDetails: 'Basic Details',
          packageType: 'Package Type',
          notificationSettingsUpdated: 'Notification settings updated successfully',
          orderCreated: 'Triggered when a new order is successfully created in the system.',
          orderUpdated: 'Triggered when changes are made to an existing order.',
          orderCancelled: 'Triggered when an order is cancelled by the user or admin.',
          orderPickedUp: 'Triggered when the courier picks up the order from the sender.',
          orderDelivered: 'Triggered when the order is successfully delivered to the recipient.',
        },
        settingsUIConfiguration: {
          uiConfigUpdated: 'UI configuration settings updated successfully',
        },
        settingsMainModule: {
          accountSettings: 'Account settings',
          operationSettings: 'Operation settings',
          users: 'Users',
          importExport: 'Import/Export',
        },
      },
      emptyState: {
        title: 'No customer found',
        description: 'Please add new customer',
        link: 'Add customer',
      },
      services: {
        colDefs: {
          serviceName: 'Service Name',
          serviceLevel: 'Service Level',
        },
        emptyState: {
          title: 'No services found',
          description: 'Please add new service',
          link: 'Add service',
        },
        searchPlaceholder: 'Search service',
        assignServices: 'Assign Services',
        searchServiceName: 'Search service name',
        unassignedServices: 'Unassigned Services',
        noServices: 'No services',
        noServicesAvailableToAssign: 'No services available to assign',
        assignedServices: 'Assigned Services',
        noServicesAssigned: 'No services assigned',
        noServicesAvailable: 'No services available',
      },
    },
  },
  notifications: {
    languageChanged: 'Language changed to {{language}}',
    languageChangeDescription: 'The interface language has been updated.',
    deliveryAdded: 'New delivery added',
    currentDeliveries: 'Current pending deliveries: {{count}}',
    userChanged: 'User changed to {{name}}',
    welcomeUser: 'Welcome, {{name}}! Enjoy your dashboard experience.',
    warning: 'Warning',
    warningDescription: 'This action cannot be undone. Please proceed with caution.',
  },
  contextMenuItems: {
    customer: {
      open: 'Open',
      assignTo: 'Assign to',
      assignToSubA: 'driver A',
      assignToSubB: 'driver B',
      updateStatus: 'Update status',
    },
  },
  sidebar: {
    customer: 'Customer',
    customers: 'Customers',
    partner: 'Partner',
    billing: 'Billing',
    logistic: 'Logistic',
    orders: 'Orders',
    prices: 'Prices',
    dispatcher: 'Dispatcher',
    routes: 'Routes',
    vehicle: 'Vehicle',
    location: 'Location',
    locations: 'Locations',
    settings: 'Settings',
    general: 'General',
    partners: 'Partners',
    templates: 'Templates',
    address: 'Address',
    zone: 'Zone',
    zoneLookupTable: 'Zone Table',
    pricesSets: 'Prices Sets',
    pricesModifiers: 'Prices Modifiers',
    addressAccess: 'Address Access',
    pricesAccess: 'Prices Access',
    invoiceAccess: 'Invoice Access',
    demo: 'Demo',
    historyGrid: 'History Grid',
    orderHistory: 'Order History',
  },
  paymentPage: {
    header: {
      title: 'Payments',
      searchPlaceholder: 'Search payment',
      addPaymentBtn: 'Add Payment',
    },
    colDefs: {
      customer: 'Customer',
      referenceNumber: 'Reference number',
      date: 'Date',
      amount: 'Amount',
      invoiceNumber: 'Invoice number',
      type: 'Type',
      created: 'Created',
      action: 'Action',
    },
    contextMenu: {
      addPayment: 'Add Payment',
      download: 'Download',
    },
    alert: {
      confirmDelete: 'Are you sure you want to delete this payment?',
      confirmDeleteMessage:
        'this action cannot be undone. Deleting this payment will remove all their details and history from your records.',
    },
    emptyState: {
      title: 'No Payments Found',
      description: 'To get started,',
      link: 'Add new payment',
    },
  },
  paymentOperationsPage: {
    header: {
      addTitle: 'Add Payment',
      editTitle: 'Edit Payment',
    },
    form: {
      labels: {
        customer: 'Customer',
        paymentMethod: 'Payment method',
        dateReceived: 'Date received',
        paymentAmount: 'Amount',
        addCredit: 'Add credit',
        memo: 'Memo',
        referenceNumber: 'Reference Number',
      },
      placeholders: {
        selectPaymentMethod: 'Select payment method',
        memoPlaceholder: 'Write your note here...',
        amountPlaceholder: '$0.00',
        referenceNumberPlaceholder: '012345678901',
      },
      validation: {
        paymentMethodRequired: 'Payment method is required',
        dateReceivedRequired: 'Date received is required',
        paymentAmountRequired: 'Amount is required',
        creditAmountRequired: 'Credit amount is required',
        referenceNumberRequired: 'Reference number is required',
      },
    },
    invoicesGrid: {
      colDefs: {
        invoiceNumber: 'Invoice number',
        date: 'Date',
        amount: 'Amount',
        amountDue: 'Amount due',
      },
      emptyState: {
        title: 'No invoices found',
        description: 'No invoices found for this customer',
      },
    },
    paymentMethods: {
      creditCard: 'Credit Card - SecurePay Solutions',
      paypal: 'PayPal - InstantPay Services',
      bankTransfer: 'Bank Transfer - SwiftBanking Network',
      credit: 'Credit',
    },
  },
  columnMange: {
    title: 'Title',
    uncheckColumn: 'Uncheck the column name to hide it.',
    selectAll: 'Select all',
    resetToDefault: 'Reset to default',
    apply: 'Apply',
    noColumns: 'No columns found.',
    keepVisibleWarn: 'Please keep at least one column visible',
    errorWhileUpdating: 'Something went wrong while updating columns',
    columnManagerTooltip:
      'Rearrange columns and toggle their visibility to customize your grid view.',
  },
  historyGrid: {
    property: 'Property',
    oldValue: 'Old Value',
    newValue: 'New Value',
    dateTime: 'Date & Time',
    modifiedBy: 'Modified By',
    title: 'History',
    noHistoryFound: 'No history records found',
  },
  searchFilterBox: {
    apply: 'Apply',
    clearAll: 'Clear all',
    addFilter: 'Add filter',
    selectOption: 'Please select option',
    selectCondition: 'Select condition',
    enterValue: 'Please enter value',
    noMultiSpaces: 'Multi spaces not allowed',
    enterInput: 'Please enter input',
    enterNumber: 'Please enter number',
    filterNumber: 'Enter number you want to filter',
    selectDate: 'Please select date',
    selectAnyOne: 'Please select any one',
    selectField: 'Select field',
    advancedFilter: 'Advanced filter',
    selectFields: 'Select fields',
    setAsQuickFilter: 'Save as quick filter',
    operators: {
      string: {
        contains: 'Contains',
        notContains: 'Not Contains',
        startsWith: 'Starts with',
        endsWith: 'Ends with',
        equals: 'Equals',
        notEquals: 'Not Equals',
      },
      number: {
        equals: 'Equals',
        notEquals: 'Not Equals',
        greaterThan: 'Greater Than',
        lessThan: 'Less Than',
        greaterThanOrEqual: 'Greater Than or Equal',
        lessThanOrEqual: 'Less Than or Equal',
      },
    },
  },
  customerAddressPage: {
    addAddress: 'Add Address',
    searchPlaceholder: 'Search address',
    modal: {
      addAddress: 'Add Address',
      editAddress: 'View Address',
      AddAddressDescription: 'Fill in the details to add a new address.',
      EditAddressDescription: 'Edit the details of your address below.',
    },
    emptyState: {
      title: 'No address found',
      description: 'Please add new address',
      link: 'Add address',
    },
    colDefs: {
      name: 'Name',
      companyName: 'Company Name',
      addressLine1: 'Address Line 1',
      addressLine2: 'Address Line 2',
      phone: 'Phone',
      city: 'City',
      postalCode: 'Postal Code',
      email: 'Email',
      action: 'Action',
    },
    contextMenu: {
      newAddress: 'Add Address',
      duplicateAddress: 'Duplicate Address',
    },
    notifications: {
      confirmDelete: 'Are you sure you want to delete this address?',
      successDelete: 'Address deleted successfully',
      failedDelete: 'Failed to delete address',
      confirmDeleteMessage:
        'This action cannot be undone. Deleting this address will remove all their details and history from your records.',
      confirmDuplicate: 'Are you sure you want to duplicate this address?',
      confirmDuplicateMessage: 'A duplicate entry will be created.',
      successCreate: 'Address created successfully',
      successUpdate: 'Address updated successfully',
      successDuplicated: 'Address duplicated successfully',
      failedDuplicate: 'Failed to duplicate address',
    },
    operationalForm: {
      customer: 'Customer',
      customerPlaceholder: 'Select customer',
      customerError: 'Please select any customer',
      name: 'Name',
      namePlaceholder: 'John Doe',
      nameError: 'Name is required',
      companyName: 'Company name',
      companyNamePlaceholder: 'abc pvt. ltd.',
      companyNameError: 'Company name is required',
      email: 'Email',
      emailPlaceholder: '<EMAIL>',
      emailError: 'Email is required',
      emailTypeError: 'Enter valid email',
      phoneNumber: 'Phone number',
      phoneNumberPlaceholder: '(*************',
      phoneNumberError: 'Phone number is required',
      validPhoneNumberError: 'Please enter a valid phone number.',
      enterValidFaxNumber: 'Please enter a valid fax number',
      phoneExt: 'Phone extension',
      phoneExtPlaceholder: '567',
      phoneExtError: 'Phone extension is required ',
      locationDividerText: 'Location details',
      addressLine1: 'Address line 1',
      addressLine1Placeholder: '123 Main st',
      addressLine1Error: 'Address line 1 is required ',
      addressLine2: 'Address line 2',
      addressLine2Placeholder: 'Apt 2',
      city: 'City',
      cityError: 'City is required',
      cityPlaceholder: 'E.g. Saint main st.',
      province: 'Province',
      provinceError: 'Province is required ',
      provincePlaceholder: 'E.g. Montreal',
      postalCode: 'Postal code',
      postalCodePlaceholder: 'E.g. H4Y H4M',
      postalCodeError: 'Postal code is required',
      validPostalCodeError: 'Please enter a valid postal code',
      country: 'Country',
      countryPlaceholder: 'E.g. Canada',
      countryError: 'Country is required',
      zone: 'Zone',
      zonePlaceholder: 'Zone Y',
      zoneError: 'Zone is Required',
      comments: 'Notes',
      commentsDividerTex: 'Notes',
      commentsPlaceHolder: 'write your note here...',
      notesTooltip: 'Use this field to add any additional information related to the address.',
      zoneToolTip:
        'Your delivery zone is automatically determined by your postal code. Please ensure your postal code is in our service area before adding an address.',
    },
  },
  vehiclePage: {
    header: {
      title: 'Vehicle',
      addNewVehicleBtnText: 'Add New Vehicle',
      addNewTimeClockSessionBtnText: 'Add Session',
      searchSession: 'Search session',
      searchVehicle: 'Search vehicle',
    },
    form: {
      totalDistance: 'Total distance',
      sameTimeError: 'Cannot add session with same date and time',
    },
    timeClockSessionForm: {
      driverName: {
        label: 'Driver name',
        errorMessage: 'Please select driver',
        placeholder: 'Select driver',
      },
      distance: {
        label: 'Distance',
        errorMessage: 'Distance is required',
      },
      dateTime: {
        label: 'Date and time',
        errorMessage: 'Please select date and time',
      },
      manual: 'Manual',
      automatic: 'Automatic',
    },
    tabs: {
      general: 'General',
      timeClockSession: 'Time Clock Sessions',
      tooltipText: 'Please add a vehicle to enable access to this tab.',
    },
    emptyState: {
      title: 'No vehicles found',
      description: 'Create a new vehicle',
      link: 'Add vehicle',
      timeClockTitle: 'No sessions found',
      timeClockDescription: 'To get started ',
      timeClockLink: 'Create a new session',
    },
    notificationMessages: {
      successAdded: 'Vehicle added successfully',
      successDelete: 'Vehicle deleted successfully',
      successUpdate: 'Vehicle updated successfully',
      sessionSuccessAdded: 'Session added successfully',
      sessionSuccessDelete: 'Session deleted successfully',
      sessionSuccessUpdate: 'Session updated successfully',
      failedToLoad: 'Failed to load vehicles',
      failedToAdd: 'Failed to add new vehicle',
      failedDelete: 'Failed to delete vehicle',
      failedUpdate: 'Failed to update vehicle',
      failedSessionUpdate: 'Failed to update session',
      failedSessionSessionAdd: 'Failed to add new session',
      failedSessionDelete: 'Failed to delete session',
      sessionOverlaps: 'Time clock session overlaps with existing session',
      sessionHourLimitExceed:
        'Time clock session duration exceeds the allowed limit of {{maxDuration}} hours',
    },
    generalHeader: {
      add: 'Add Vehicle',
      edit: 'Edit Vehicle',
    },
    modal: {
      addSession: 'Add Session',
      editSession: 'Edit {{name}} Session',
      headerDesc: 'Following are form fields to add new session.',
      headerDescEdit: 'Following are form fields to edit the session.',
    },
    breedCrumbs: {
      vehicle: 'Vehicle',
      general: 'General',
      timeClockSession: 'Time Clock Sessions',
    },
    alert: {
      deleteConfirmation: 'Are you sure, you want to delete this vehicle ?',
      deleteConfirmationMessage:
        'This action cannot be undone. Deleting this vehicle will remove all their details and history from your records.',
      sessionDeleteConfirmation: 'Are you sure, you want to delete this session ?',
      sessionDeleteConfirmationMessage:
        'This action cannot be undone. Deleting this session will remove all their details and history from your records.',
      firstBtnText: 'Delete',
      secondBtnText: 'Cancel',
    },
    tooltip: {
      packageType: 'Select the types of packages the vehicle can carry: Boxes, Skid, or Custom.',
      capacity: "Enter the vehicle's max weight capacity (e.g., 3000 lb)",
      branches:
        "Specify the location or branch to which the vehicle is assigned. This helps identify the vehicle's home base for operations and logistics management.",
      ownedBy: "Enter the name of the vehicle's owner, which can be an individual or a company",
      notes:
        'Write down any additional details or observations about the vehicle, such as special instructions, maintenance needs, or operational conditions.',
    },
    colDefs: {
      entryDate: 'Entry Date',
      type: 'Type',
      capacity: 'Capacity',
      fleetId: 'Fleet ID',
      odometer: 'Odometer Reading',
      licensePlate: 'License Plate',
      make: 'Make',
      model: 'Model',
      year: 'Year',
      defaultDriver: 'Default Driver',
      action: 'Action',
    },
    labels: {
      vehicleType: 'Vehicle type',
      fleetId: 'Fleet ID',
      make: 'Make',
      model: 'Model',
      year: 'Year',
      license: 'License/Tag',
      vin: 'VIN',
      packageType: 'Package type',
      capacity: 'Capacity',
      branches: 'Branch',
      odometer: 'Odometer',
      ownedBy: 'Owned by',
      defaultDriver: 'Default driver',
      comments: 'Notes',
      basicDetails: 'Basic details',
      commentSection: 'Notes',
    },
    messages: {
      vehicleTypeRequired: 'Vehicle type is required',
      fleetIdRequired: 'Fleet ID is required',
      makeRequired: 'Make is required',
      modelRequired: 'Model is required',
      yearRequired: 'Year is required',
      maxYearExceeded: 'Max year exceeded',
      licenseRequired: 'License/Tag is required',
      vinRequired: 'VIN is required',
      packageTypeRequired: 'Package types are required',
      capacityRequired: 'Capacity is required',
      branchesRequired: 'Branch is required',
      odometerRequired: 'Odometer reading is required',
      ownedByRequired: 'Owned by is required',
      defaultDriverRequired: 'Default driver is required',
      requireEndDate: 'Please select end date and time',
      vehicleNotFound: 'Vehicle not found',
      licensePlateExists: 'Vehicle with this license plate already exists',
      invalidDecimal:
        ' Please enter a valid number with 2 decimal places (e.g., 12, 12.3, or 12.34)',
    },
    placeholders: {
      selectType: 'Select type',
      fleetId: 'CAR001',
      make: 'Dodge',
      model: 'Caravan',
      year: '1999',
      license: 'H4G 1NX',
      vin: 'D12154345334AS6FD',
      capabilities: 'Boxes',
      capacity: '3000',
      branches: 'Montreal branch',
      odometer: '15202',
      ownedBy: 'VNP',
      selectDefaultDriver: 'Select default driver',
      comments: 'Write your note here...',
    },
    alerts: {
      discardChangesTitle: 'Are you sure, you want to discard changes?',
      discardChangesMessage: 'Changes will be reset to the default value',
      discardButtonTitle: 'Discard',
    },
    contextMenuItems: {
      newVehicle: 'Add Vehicle',
      duplicateVehicle: 'Duplicate Vehicle',
      delete: 'delete',
    },
    timeClockColDefs: {
      driverName: 'Driver Name',
      vehicle: 'Vehicle',
      distance: 'Distance',
      startTime: 'Start Date & Time',
      endTime: 'End Date & time',
      totalTime: 'Total Time',
      action: 'Action',
    },
    addNameSession: 'Add {{name}} Session',
    activeSession: 'Active session',
    inactiveSession: 'Incomplete session',
    totalTimeWarning: 'Please add the end date to see the total time',
    totalTime: 'Total time',
    startTime: 'Start time',
    endTime: 'End time',
    minDistance: 'Distance should be more than 1 km',
  },
  addressPage: {
    header: {
      title: 'Address',
      searchAddress: 'Search address',
      addAddress: 'Add Address',
    },
    contextMenu: {
      newAddress: 'Add Address',
      duplicateAddress: 'Duplicate Address',
    },
    notification: {
      successAddressDelete: 'Address deleted successfully',
      successAddressAdd: 'Address created successfully',
      successAddressUpdate: 'Address updated successfully',
      successAddressDuplicate: 'Address duplicated successfully',
      failedAddressDelete: 'Failed to delete address',
      failedAddressAdd: 'Failed to create a new address',
      failedAddressUpdate: 'Failed to update address',
      failedDuplicateUpdate: 'Failed to duplicate address',
      cantDeleteDefaultAddress: 'Cannot delete a default address',
      notFound: 'Address not found',
      validationFailed: 'Validation failed',
      addressIdNotFound: 'Address id not found, please try again',
    },
    alert: {
      deleteConfirmation: 'Are you sure you want to delete this address?',
      deleteConfirmationMessage:
        'This action cannot be undone. Deleting this address will remove all their details and history from your records.',
    },
    modal: {
      addAddress: 'Add Address',
      editAddress: 'View Address',
      AddAddressDescription: 'Fill in the details to add a new address.',
      EditAddressDescription: 'Edit the details of your address below.',
    },
    emptyState: {
      title: 'No address found',
      description: 'Please add new address',
      link: 'Add address',
    },
    colDefs: {
      customer: 'Customer',
      name: 'Name',
      contact: 'Contact',
      companyName: 'Company Name',
      addressLine1: 'Address Line 1',
      addressLine2: 'Address Line 2',
      phone: 'Phone',
      city: 'City',
      province: 'Province',
      postalCode: 'Postal Code',
      email: 'Email',
      zone: 'Zone',
      action: 'Action',
    },
    operationalForm: {
      customer: 'Customer name',
      customerPlaceholder: 'Select customer',
      customerError: 'Please select any customer',
      name: 'Name',
      namePlaceholder: 'John Doe',
      nameError: 'Name is required',
      companyName: 'Company name',
      companyNamePlaceholder: 'abc pvt. ltd.',
      companyNameError: 'Company name is required',
      email: 'Email',
      emailPlaceholder: '<EMAIL>',
      emailError: 'Email is required',
      emailTypeError: 'Enter valid email',
      phoneNumber: 'Phone number',
      phoneNumberPlaceholder: '(*************',
      phoneNumberError: 'Phone number is required',
      validPhoneNumberError: 'Please enter a valid phone number.',
      phoneExt: 'Phone extension',
      phoneExtPlaceholder: '00000',
      phoneExtError: 'Phone extension is required ',
      locationDividerText: 'Location details',
      addressLine1: 'Address line 1',
      addressLine1Placeholder: '123 Main st',
      addressLine1Error: 'Address line 1 is required ',
      addressLine2: 'Address line 2',
      addressLine2Placeholder: 'Apt 2',
      city: 'City',
      cityError: 'City is required',
      cityPlaceholder: 'E.g. Saint main st.',
      province: 'Province',
      provinceError: 'Province is required ',
      provincePlaceholder: 'E.g. Montreal',
      postalCode: 'Postal code',
      postalCodePlaceholder: 'E.g. H4Y H4M',
      postalCodeError: 'Postal code is required',
      validPostalCodeError: 'Please enter a valid postal code',
      country: 'Country',
      countryPlaceholder: 'E.g. Canada',
      countryError: 'Country is required',
      zone: 'Zone',
      zonePlaceholder: 'E.g. Zone Y',
      zoneError: 'No zone found with given {{code}} postal code.',
      comments: 'Notes',
      commentsDividerTex: 'Notes',
      commentsPlaceHolder: 'write your note here...',
      noPostalCodeFound: 'No postal code found for this address to load zone',
      noCoordinatesFound: 'No coordinates found for this address',
    },
  },
  zonePage: {
    newZone: 'New zone',
    editZone: 'Edit Zone',
    duplicateZone: 'Duplicate zone',
    addZone: 'Add Zone',
    enterDetails: 'Enter the details of the new zone below.',
    basicDetails: 'Basic details',
    seedZones: 'Seed zones',
    noZonesFound: 'No zones found',
    createNewZone: 'Create a new zone to get started',
    failedToLoadZones: 'Failed to load Zones',
    confirmDeleteZone: 'Are you sure you want to delete this?',
    atLeastOnePostalCode: 'At least one postal code is required.',
    nameValidation: 'Name must contain only letters, digits, and spaces.',
    ok: 'Ok',
    gotIt: 'Got it',
    createZoneGuidelines: 'Create Zone - Guidelines',
    click: 'Click',
    pressThe: 'Press the',
    buttonToBegin: 'button to begin.',
    fillInTheDetails: 'Fill in the Details',
    zoneName: 'Zone Name:',
    zoneNameField: 'Zone name',
    enterUniqueZoneName: 'Enter a unique name for your zone.',
    postalCodes: 'Postal Codes:',
    guidelinesForZones: 'Guidelines and information for creating and managing zones.',
    addMultiplePostalCodes:
      'Enter one or multiple postal codes, separated by commas or press Enter to add. Postal codes already assigned to another zone will be highlighted in blue to indicate an overlap.',
    notes: 'Notes:',
    addAdditionalInfo: 'Add any additional information about this zone (optional).',
    saveYourZone: 'Save your zone',
    clickSaveToFinalize:
      'Once all the details are added, click Save to finalize the zone creation.',
    howZoneWorks: 'How Zone Works?',
    codeIsAddedInAnotherZone: '{{code}} is added in another zone as well.',
    codesAreAddedInAnotherZone: '{{code}} are added in another zone as well.',

    notification: {
      successAdded: 'Zone created successfully',
      successUpdated: 'Zone updated successfully',
      successDeleted: 'Zone deleted successfully',
    },

    zoneLookUp: {
      hoverOverACell: 'Hover over a cell to see its intersection.',
      pricingApplied: 'Reciprocal pricing applied successfully',
      formRevertedToOriginal: 'The form has been reverted to its original state.',
      failedToLoad: 'Failed to load data',
      tableNotFound: 'Zone lookup table not found',
      emptyCellsFilled: 'Empty cells filled',
      allCellsAdjusted: 'All cells adjusted',
      tableShouldNotBeEmpty: 'Zone table name cannot be empty. Please provide a unique name',
      changesSavedToTable: 'Changes saved successfully to the zone lookup table',
      zoneLookUpTableSaved: 'Zone lookup table saved successfully',
      anErrorOccurredWhileSaving:
        'An error occurred while saving. Please try again or check your connection',
      changesDiscarded: 'Changes discarded',
      zones: 'Zones',
      enterValue: 'Enter value',
      howToUse: 'How to Use the Zone Lookup Table',
      gotIt: 'Got It',
      keyboardNavigation: 'Keyboard Navigation',
      click: 'Click',
      activateCell: 'on a cell to activate it for editing.',
      useThe: 'Use the ',
      arrowKeys: 'Arrow Keys',
      whenCellIsActive: ' to move between cells when a cell is active.',
      press: 'Press',
      escape: 'Escape',
      deactivateCell: 'to deactivate the current cell.',
      tipsForUsage: 'Tips for Efficient Usage',
      hoverBefore: 'Hover',
      hoverStrong: 'over',
      hoverAfter: 'cells to see their intersection for better clarity.',
      applyReciprocalPricingBefore: 'Use',
      applyReciprocalPricingStrong: 'Apply Reciprocal Pricing',
      applyReciprocalPricingAfter:
        'to quickly fill matching symmetric cells (e.g., Zone A → Zone B will also populate Zone B → Zone A).',
      autoFillEmptyCellsBefore: 'Use',
      autoFillEmptyCellsStrong: 'Auto-Fill Empty Cells',
      autoFillEmptyCellsAfter: 'to set a default value for all blank cells in the grid.',
      bulkAdjustGridValuesBefore: 'Use',
      bulkAdjustGridValuesStrong: 'Bulk Adjust Grid Values',
      bulkAdjustGridValuesAfter:
        'to apply a fixed or percentage adjustment to all populated cells.',
      advancedFeatures: 'Advanced Features',
      keyboardShortcutsBefore: 'Keyboard Shortcuts: ',
      useShortcuts: 'Use shortcuts for faster navigation and actions.',
      ctrlZBefore: 'Ctrl + Z: ',
      ctrlZAfter: 'Undo changes.',
      ctrlYBefore: 'Ctrl + Y: ',
      ctrlYAfter: 'Redo changes.',
      bulkAdjustModalTitle: 'Bulk Adjust Grid Values',
      bulkAdjustModalDescription: 'Adjust all populated cells in the price grid by this amount.',
      adjustmentAmountLabel: 'Adjustment Amount (Example: 8.75)',
      treatAsFixedLabel: 'Treat as fixed',
      treatAsPercentageLabel: 'Treat as percentage',
      autoFillEmptyCellsModalDescription:
        'This will populate all empty cells in the grid with the value you specify.',
      autoFillEmptyCellsModalPlaceholder: 'Enter a value to fill all empty cells:',
      createZoneLookupTable: 'Create Zone Lookup Table',
      editZoneLookupTable: 'Edit Zone Lookup Table',
      enterUniqueName: 'Transit app zone table',
      help: 'Help',
      applyReciprocalPricing: 'Apply Reciprocal Pricing',
      autoFillEmptyCells: 'Fill Empty Cells',
      autoFillEmptyCellsButton: 'Fill empty cells',
      bulkAdjustGridValue: 'Adjust all cells',
      saveZoneLookupTable: 'Save the zone lookup table',
      update: 'Update',
      save: 'Save',
      discardChanges: 'Discard changes',
      currentlyNoZonesAdded: 'Currently, No zones are added.',
      addZonesToCreateLookupTable: 'Add zones to create a lookup table.',
      addZones: 'Add Zones',
      confirmDeleteZoneTable: 'Are you sure you want to delete {{this}}?',
      zoneTableDeletedSuccessfully: 'Zone table deleted successfully',
      confirmDuplicateZoneTable: 'Are you sure you want to duplicate this zone table?',
      newZoneTable: 'New zone table',
      duplicateZoneTable: 'Duplicate zone table',
      addZoneTable: 'Add zone table',
      noZoneTables: 'No Zone Tables',
      noZoneTablesFound: 'No Zone Tables found',
      addTable: 'Add Table',
      failedToLoadZoneTables: 'Failed to load Zone Tables',
      zoneLookupTableGrid: 'Zone Lookup Table Grid',
      fillEmptyCellsWithAmount: 'Fill all empty cells in the price grid with this amount.',
      adjustPopulatedCellsByAmount: 'Adjust all populated cells in the price grid by this amount.',
      howToUseZoneTable: 'How to use zone table?',
      reversePricing: 'Reverse pricing',
      bulkAdjustGridValues: 'Bulk Adjust Grid Values',
      submit: 'Submit',
      amount: 'Amount',
      valueCannotBeFilled: 'Value cannot be filled.',
      allCellsAlreadyFilled: 'All cells are already filled up.',
      valueCannotBeAdjusted: 'Value cannot be adjusted.',
      allCellsEmpty: 'All cells are empty.',
      enterZoneTableNameAndFillCell:
        'Enter a name and fill up at least one cell to create zone table.',
      pleaseAddModifiersToGroup: 'Please add one or more modifiers to the group.',
    },
    emptyState: {
      link: 'Add zone',
    },

    header: {
      title: 'Zone',
    },
    colDefs: {
      id: 'ID',
      name: 'Name',
      postalCodes: 'Postal codes',
      action: 'Action',
      comment: 'Comment',
    },
    operationalForm: {
      name: 'Name',
      namePlaceholder: 'Enter zone name',
      nameError: 'Name is required',
      postalCodes: 'Postal Codes',
      postalCodesPlaceholder: 'Enter comma-separated postal codes',
      postalCodesError: 'Postal codes are required',
      comments: 'Comments',
      commentsDividerText: 'Comment',
      commentsPlaceholder: 'Additional details about this zone',
      delete: 'Delete',
    },
  },
  spinner: {
    loading: 'Loading...',
  },
  statusFallbackPage: {
    backHome: 'Back Home',
    fallBackMessage: {
      unauthorizedAccess: 'Sorry, you are not authorized to access this page.',
      pageNotExist: 'Sorry, the page you visited does not exist.',
      somethingWentWrong: 'Sorry, something went wrong.',
    },
  },
  priceSetPage: {
    errorWhileChangingModifierConfig: 'Something went wrong while updating modifier configuration',
    header: {
      title: 'Price Set',
      search: 'Search price sets',
      addPriceSetBtn: 'Add Price Set',
      addPriceSet: 'Add Price Set',
      editPriceSet: 'Edit Price Set',
    },
    form: {
      name: 'Service name',
      namePlaceholder: 'VNP 4 hours Fuel Charges',
      serviceLevel: 'Service level',
      serviceNameRequired: 'Service name is required',
      serviceLevelRequired: 'Service level is required',
      serviceLevelPlaceholder: '4 hr express',
      paymentOption: 'Payment option',
      paymentOptionPlaceholder: 'Select payment option',
      notes: 'Notes',
      notesPlaceholder: 'Notes added here will be visible to the customer as well.',
      description: 'Description',
      descriptionPlaceHolder: 'Description added here will only be visible to the admins.',
      selectZoneTableGridPlaceholder: 'Select zone table',
      editZoneTableBtn: 'Edit this zone table',
      noServicesAssigned: 'No services assigned',
      toAssignServiceClick: 'To assign service, click',
      here: 'here',
    },
    helpModalStrings: {
      modalTitle: 'How price modifiers work in price set editor',
      modalDescription:
        'The price set editor lets you configure and assign multiple price modifiers to adjust pricing dynamically.',
      buttonText: 'Ok, Got it',

      addPriceModifiersTitle: 'Add price modifiers',
      addPriceModifiersDescription: 'Click',
      addPriceModifiersStrong: 'Add Price Modifiers',
      addPriceModifiersEnd: 'to choose predefined or custom modifiers.',

      configureDefaultBehaviorTitle: 'Configure default behavior',
      configureDefaultBehaviorDescription: 'Use the',
      configureDefaultBehaviorStrong: 'Configuration by default',
      configureDefaultBehaviorEnd: 'to define how a modifier behaves',

      noneLabel: 'None: ',
      noneDescription: 'Modifier is disabled.',

      selectedLabel: 'Selected: ',
      selectedDescription: 'Modifier is optional but active by default.',

      requiredLabel: 'Required: ',
      requiredDescription: 'Modifier is mandatory.',

      exampleTitle: 'Example',
      exampleDescription: 'Apply a surcharge for items over 96 inches:',
      exampleStep1: 'Add Length price calculations for 96 inches and over.',
      exampleStep2: 'Set it to Required in the default behavior.',
    },
    tabs: {
      general: 'General',
      schedule: 'Schedule',
      basePriceByZone: 'Base Price By Zone',
      priceModifier: 'Price Modifiers',
      customers: 'Customers',
      history: 'History',
    },
    colDefs: {
      name: 'Name',
      service: 'Service',
      configByDefault: 'Configuration By Default',
    },
    options: {
      none: 'None',
      selected: 'Selected',
      required: 'Required',
    },
    customers: {
      colDefs: {
        companyName: 'Company Name',
        customerName: 'Customer Name',
      },
      assignCustomersTitle: 'Assign Customers',
      assignCustomersBtn: 'Assign Customers',
      assignedCustomers: 'Assigned Customers',
      unassignedCustomers: 'Unassigned Customers',
      searchPlaceholder: 'Search customer',
      noCustomersFound: 'No customers found',
      noCustomersAssigned: 'No customers assigned',
      noCustomersAvailable: 'No customers available',
      noCustomersAvailableToAssign: 'No customers available to assign',
      toGetStarted: 'To get started,',
    },
    schedule: {
      availabilityOptions: {
        never: 'Never',
        weekly: 'Weekly',
        always: 'Always',
      },
      notification: {
        scheduleSaved: 'Schedule saved successfully',
      },
      offsetTypeOptions: {
        to: 'To',
        by: 'By',
      },
      week: {
        includes: 'Including weekends',
        excludes: 'Excluding weekends',
      },
      form: {
        priceSetAvailability: 'Price set availability',
        maxScheduleLimitInfoMessage: 'You can add a maximum of {{limit}} schedules.',
        days: 'Days',
        startTime: 'Start time',
        endTime: 'End time',
        requiredTime: 'Please select schedule time.',
        selectDaysPlaceholder: 'Select days',
        requiredDays: 'Please select days.',
        addScheduleBtn: 'Add Schedule',
        offsetDueDate: 'Offset due date',
        hours: 'Hour(s)',
        hoursPlaceholder: '00',
        requiredHours: 'Hours is required',
        minutes: 'Minute(s)',
        minutesPlaceholder: '00',
        requiredMinutes: 'Minutes is required',
        daysOut: 'Day(s) out',
        daysOutPlaceholder: '00',
        requiredDaysOut: 'Days out is required',
        sameTimeError: 'Cannot set the same schedule time',
        minuteLimitError: 'Minutes must not be greater than 59',
      },
    },
    priceMod: {
      searchPlaceholder: 'Search price modifiers',
      assignPriceModBtn: 'Assign Price Modifiers',
      notFound: 'Price modifiers not found',
      assignToSet: 'No price modifier assigned.',
      toAssignMod: 'To assign price modifiers,',
      getStarted: 'To get started,',
      clickHear: 'Click here.',
      addSet: 'add price modifier.',
      assignModifierWithBack: 'Assign Modifiers',
      unassignMods: 'Unassigned modifiers',
      assignMods: 'Assigned Modifiers',
      noModsAssigned: 'No modifiers assigned.',
      noModsFoundToAssigned: 'No modifier available to assign.',
      notAvailable: 'No modifiers available.',
    },
    tooltip: {
      tabRestriction: 'Please add a price set to enable access to this tab.',
      notes: 'Notes added here will be visible to the customer as well.',
      description: 'Description added here will only be visible to the admins.',
      disableZoneTableSaveBtn:
        'Select zone table from dropdown or change cell value to assign zone table to this price set.',
    },
    alert: {
      duplicateConfirmation: 'Are you sure you want to duplicate this price set?',
      deleteConfirmation: 'Are you sure you want to delete this price set?',
      overWriteZoneTableConfirmationTitle:
        'Are you sure you want to overwrite the current zone table values with selected one?',
      overWriteZoneTableConfirmationMsg:
        'This action will replace the existing values with new ones.',
      updateZoneTableConfirmationMsg: 'This action will update the existing values with new ones.',
      deleteConfirmationMessage:
        'This action cannot be undone. Deleting this price set will remove all their details and history from your records.',
      overWriteZoneTableOnSave: 'Are you sure you want to update the zone table values?',
    },
    notification: {
      successAdded: 'Price set added successfully',
      successDelete: 'Price set deleted successfully',
      successEdit: 'Price set updated successfully',
      successAssignedZoneTable: 'Zone table assigned successfully to this price set',
      errorAssignZoneTable: 'Failed to assign Zone table to this price set',
    },
    contextMenu: {
      addPriceSet: 'Add New Price Set',
      duplicatePriceSet: 'Duplicate Price Set',
      assignUnassign: 'Assign or Unassign to Customer',
    },
    emptyState: {
      title: 'No price sets found',
      description: 'Please add new price set',
      link: 'Add price set',
    },
  },
  priceModifiers: {
    updateModifier: 'Update Modifier',
    addModifier: 'Add Modifier',
    addModifierGroup: 'Add Modifier Group',
    confirmDeletePriceModifier: 'Are you sure you want to delete this price modifier?',
    deleteModifierWarning:
      'This action cannot be undone. Deleting this price modifier will remove all their details and history from your records.',
    priceModifierDeleted: 'Price modifier deleted successfully',
    duplicate: 'Duplicate',
    fixedAmount: 'Fixed amount of $0.00\nFormula:',
    fixedAmountLabel: 'Fixed amount',
    fixedPercent: 'Fixed percent',
    fixedOverageAmount: 'Fixed overage amount',
    fixedOveragePercent: 'Fixed overage percent',
    tieredFixedOverageAmount: 'Tiered fixed overage amount',
    tieredFixedOveragePercent: 'Tiered fixed overage percent',
    incrementalOverageAmount: 'Incremental overage amount',
    incrementalOveragePercent: 'Incremental overage percent',
    tieredIncrementalOverageAmount: 'Tiered incremental overage amount',
    tieredIncrementalOveragePercent: 'Tiered incremental overage percent',
    basePrice: 'Base price',
    declaredValue: 'Declared value',
    cubicDimensions: 'Cubic dimensions',
    distance: 'Distance',
    height: 'Height',
    width: 'Width',
    length: 'Length',
    quantity: 'Quantity',
    collectionWaitTime: 'Collection wait time (seconds)',
    deliveryWaitTime: 'Delivery wait time (seconds)',
    priceModifierSaved: 'Price modifier saved successfully',
    priceModifierNotSaved: 'Price modifier not saved',
    pricingMethod: 'Pricing method',
    selectPriceMethod: 'Select price method',
    initialValue: 'Initial value',
    maximumValueExceeded: 'Maximum value exceeded',
    applicableRange: 'Applicable range',
    valueMustBeGreaterThanApplicableRangeFrom: 'Value must be greater than Applicable Range From',
    maximumPercentExceeded: 'Maximum percent exceeded',
    configureTiers: 'Configure tiers',
    summary: 'Summary',
    commonTwoHoursSkidPriceCalculation: 'Common 2 Hours Skid (QTY) price Calculation',
    calculationBasedOn: 'Calculation based on',
    selectBaseField: 'Select base field',
    to: 'To',
    stepValue: 'Step value',
    summaryDetails: 'Summary details',
    calculationType: 'Calculation type',
    adjustmentTypeLabel: 'Adjustment amount',
    noPriceModifierFound: 'No price modifier found',
    pleaseAddNewPriceModifier: 'Please add new price modifier',
    addPriceModifier: 'Add price modifier',
    colDefs: {
      kind: 'Kind',
      name: 'Name',
    },
    configureTiersForm: {
      add: 'Add',
      startValueIsRequired: 'Start value is required',
      pleaseEnterValidNumber: 'Please enter a valid number',
      endValueIsRequired: 'End value is required',
      endValueShouldBeGreaterThanStartValue: 'End value should be greater than start value',
      end: 'End',
      start: 'Start',
      amountIsRequired: '{{Amount}} is required',
      adjustmentAmountIsRequired: 'Amount is required',

      defaultAmountIfNoMatchingTiers: 'Default {{amount}} if there are no matching tiers:',
      amount: 'Amount',
      percentage: 'Percentage',
      basePriceTiers: 'Base Price Tiers',
    },
    groupModifiers: {
      behavior: 'Behavior',
      addPriceModifiersGroup: 'Add price modifiers group',
      vnpFourHoursFuelCharges: 'VNP 4 hours fuel charges',
      selectBehavior: 'Select behavior',
      descriptionVisibleToAdminsOnly: 'Description added here will only be visible to the admins.',
      unassignedModifiers: 'Unassigned modifiers',
      noModifiersAvailable: 'No modifiers available.',
      assignedModifiersToGroupList: 'Assigned modifiers to group list',
      searchModifiers: 'Search modifiers',
      noModifiersAssigned: 'No modifiers assigned.',
      priceModifierGroup: 'Price modifier group',
      priceModifier: 'Price modifier',
      modifiersGroupList: 'Modifiers group list',
      assignModifiers: 'Assign Modifiers',
      noSavedGroupModifiersFound: 'No saved group modifiers found',
      groupModifierCreatedSuccessfully: 'Group modifier created successfully',
      groupModifierUpdatedSuccessfully: 'Group modifier updated successfully',
      updatePriceModifiersGroup: 'Update price modifiers group',
      useHighestPricedModifier: 'Use the highest priced modifier',
      useLowestPricedModifier: 'Use the lowest priced modifier',
      useSumOfAllModifiers: 'Use the sum of all modifiers',
    },
    formulaDescription: {
      forFlatRate: 'Add a ${{rate}} flat fee to all deliveries.',
      forFlatPercent: 'Add a {{percent}}% surcharge to the {{calculationBasedOn}} of the order.',
      surcharge: 'Surcharge',
      exceeds: 'Exceeds',
      isGreaterThan: 'Is greater than',
      isMoreThan: 'Is more than',
      greaterThan: 'Greater than',
      greaterThanOrEqual: 'Greater than or equal',
      lessThan: 'Less than',
      lessThanOrEqual: 'Less than or equal',
      ifThe: 'If the',
      addA: 'Add a',
      from: 'From',
      to: 'To',
      pleaseSelectBehavior: 'Please select behavior',
      pricingMethodRequired: 'Pricing method is required',
      calculationFieldRequired: 'Calculation field is required',
      initialValueRequired: 'Initial value is required',
      minApplicableRangeRequired: 'Minimum applicable range is required',
      maxApplicableRangeRequired: 'Maximum applicable range is required',
      stepValueRequired: 'Step value is required',
      adjustmentTypeRequired: 'Adjustment type is required',
    },
  },
  systemErrors: {
    unknown: 'Generic unknown system error',
    validationError: 'Validation error',
    configurationError: 'System configuration error',
    unknownException: 'Something went wrong, please refresh the page and try again.',
    whileAssigningServices: 'Something went wrong while assigning price sets to the customer',
    authErrors: {
      authenticationRequired: 'Authentication required',
      insufficientPermissions: 'Insufficient permissions',
      invalidCredentials: 'Invalid credentials provided',
      tokenExpired: 'Session expired. please login again.',
      tokenInvalid: 'Invalid authentication token',
      tokenMissing: 'Missing authentication token',
    },
    dataErrors: {
      resourceNotFound: 'Resource not found',
      duplicateEntry: 'Duplicate entry',
      invalidDataFormat: 'Invalid data format',
      dataConstraintViolation: 'Data constraint violation',
    },
    businessErrors: {
      setting: {
        notFound: 'Settings not found',
      },
      tenant: {
        notFound: 'Tenant not found',
        alreadyExists: 'Tenant already exists',
        inactive: 'Tenant is inactive',
        deleted: 'Tenant is deleted',
        invalidStatus: 'Invalid tenant status',
        operationNotAllowed: 'Operation not allowed on tenant',
        uniqueConstraintViolation: 'Unique constraint violation for tenant',
      },
      user: {
        notFound: 'User not found',
        alreadyExists: 'User with this email already exists',
        inactive: 'User is inactive',
        deleted: 'User is deleted',
        emailExists: 'Email already exists',
        invalidStatus: 'Invalid user status',
        operationNotAllowed: 'User operation not allowed',
        credentialsExpired: 'User credentials expired',
      },
      vehicle: {
        notFound: 'Vehicle not found',
        alreadyExists: 'Vehicle already exists',
        inactive: 'Vehicle is inactive',
        deleted: 'Vehicle is deleted',
        invalidStatus: 'Invalid vehicle status',
        operationNotAllowed: 'Vehicle operation not allowed',
        maintenanceOverdue: 'Vehicle maintenance overdue',
      },
      vehicleType: {
        notFound: 'Vehicle type not found',
        alreadyExists: 'Vehicle type already exists',
        inactive: 'Vehicle type is inactive',
        deleted: 'Vehicle type is deleted',
        invalidStatus: 'Vehicle type invalid status',
        operationNotAllowed: 'Vehicle type operation not allowed',
        inUse: 'Vehicle type in use',
      },
      priceModifiers: {
        priceModifierExists: 'Price modifier with this name already exists',
        cannotDeletePriceModifier:
          'Cannot delete this price modifier as it is already in use in another group modifier',
        groupModifierDeletedSuccessfully: 'Group modifier deleted successfully',
        groupModifierExists: 'Group modifier with this name already exists',
        cannotDeleteGroupModifier:
          'Cannot delete this group modifier as it is already in use in another group modifier',
      },
      zone: {
        zoneNameExists: 'Zone with this name already exists.',
      },
      zoneTable: {
        zoneTableExists: 'Zone table with this name already exists.',
      },
    },
    externalServiceErrors: {
      generic: 'Generic external service error',
      httpRequestFailed: 'HTTP request failed',
      databaseError: 'Database operation failed',
      fileUploadFailed: 'File upload failed',
      fileNotFound: 'File not found',
      emailSendFailed: 'Email sending failed',
    },
    infrastructureErrors: {
      generic: 'Generic infrastructure error',
      networkError: 'Network communication error',
      storageError: 'Storage system error',
      cacheError: 'Cache system error',
      queueError: 'Message queue error',
    },
  },
  quickFilter: {
    quickFilterAddedSuccessfully: 'Quick filter added successfully',
    quickFilterDeletedSuccessfully: 'Quick filter deleted successfully',
    quickFilterLabel: 'Quick Filter',
    emptyFieldError: 'Please fill the correct fields',
    quickFilterExists: 'Quick filter with this name already exists',
  },
  fallBackPages: {
    unauthorized: 'Sorry, you are not authorized to access this page.',
    pageNotExist: 'Sorry, the page you visited does not exist.',
    unknown: 'Sorry, something went wrong.',
  },
  ordersPage: {
    noOrdersFound: 'No orders found',
    appliedFilter: 'Applied filter',
    searchOrders: 'Search orders',
    driver: 'Driver',
    selectDriverToAssign: 'Select driver to assign order',
    assign: 'Assign',
    collectionCompanyName: 'Collection Company Name',
    deliveryCompanyName: 'Delivery Company Name',
    status: 'Status',
    statusValues: {
      draft: 'Draft',
      cancelled: 'Cancelled',
      inTransit: 'In Transit',
      pending: 'Pending',
      assigned: 'Assigned',
      completed: 'Completed',
    },
    noQuickFiltersAvailable: 'No quick filters available',
    deleteQuickFilter: 'Delete Quick Filter',
    areYouSureDeleteQuickFilter: 'Are you sure you want to delete this quick filter?',
    quickFilter: 'Quick filter',
    deliveryTime: 'Delivery Time',
    deliveryCompany: 'Delivery Company',
    collectionTime: 'Collection Time',
    assignee: 'Assignee',
    setToInTransit: 'Set to In Transit',
    setToDelivered: 'Set to Delivered',
    setToPickup: 'Set to Pickup',
    updateStatus: 'Update Status',
    unassign: 'Unassign',
    sendNotification: 'Send notification',
    sendStatusToCustomer: 'Send Status to Customer',
    sendStatusToReceiver: 'Send Status to Receiver',
    sendStatusToCollector: 'Send Status to Collector',
    sendStatusToDriver: 'Send Status to Driver',
    selectDriver: 'Select driver',
    dateSubmitted: 'Date submitted',
    collectionCompany: 'Collection Company',
    trackingNumber: 'Tracking Number',
    missingField: 'Missing field',
    pricesBreakdown: 'Prices Breakdown',
    packages: 'Packages',
    attachments: 'Attachments',
    viewOrder: 'View Order',
    collectionLocation: 'Collection location',
    enterCollectionLocationDetails: 'Enter the details of collection location',
    deliveryLocation: 'Delivery location',
    enterDeliveryLocationDetails: 'Enter the details of delivery location',
    orderDetails: 'Order details',
    orderInfoDataFields: {
      trackingNo: 'Tracking no.',
      dateSubmitted: 'Date submitted',
      numberOfPackages: 'No. of packages',
      pickupDateTime: 'Pickup date & time',
      pickupSignatureRequired: 'Signature required (Pickup)',
      deliveryByDate: 'Date delivery by',
      cod: 'COD',
      deliverySignatureRequired: 'Signature required (Delivery)',
      vehicle: 'Vehicle',
      codAmount: 'COD amount',
      description: 'Description',
      driverName: 'Driver name',
      status: 'Status',
      notes: 'Notes',
    },
    orderPriceDataFields: {
      serviceLevel: 'Service level',
      priceSet: 'Price set',
      basePrice: 'Base price',
      optionsPrice: 'Options price',
      total: 'Total',
      miscAdjustment: 'Misc adjustment',
      insurance: 'Insurance',
      billingStatus: 'Billing status',
      totalDeclaredValue: 'Total declared value',
      invoiceNumber: 'Invoice number',
      paymentStatus: 'Payment status',
    },
    statisticInfoDataFields: {
      distanceKm: 'Distance (Km)',
      totalQuantity: 'Total quantity',
      combinedWeightKg: 'Combined weight (Kg)',
      deliveryWaitTime: 'Delivery wait time',
      pickupWaitTime: 'Pickup wait time',
    },
    customerInfoDataFields: {
      company: 'Company',
      customer: 'Customer',
      phone: 'Phone',
      email: 'Email',
      contact: 'Contact',
    },
    collectAt: 'Collect at',
    amountToCollect: 'Amount to collect',
    orderDetailsChange: 'Order details change',
    priceBreakDown: {
      idNotFound: 'Modifier id not found',
      colDefs: {
        appliedModifier: 'Applied Modifiers',
        appliedCharges: 'Applied Charges',
        description: 'Description',
      },
      modifierPricingButton: 'Modify Pricing',
      mustBeMoreThan1: 'Amount must be more than 1',
      customPricingModelTitle: 'Custom Price',
      nameLabel: 'Name',
      amountLabel: 'Amount',
      requiredName: 'Name is required',
      requiredAmount: 'Amount is required',
      namePlaceholder: 'Enter name',
      customPricingModelTitleEditMode: 'View Price Modifier',
      negativeOrPositiveValue: 'Amount must be negative or positive value.',
      customPricingModelDescEditMode: 'Details of applied modifier to this order',
      customPricingModelDesc:
        'Use this option to add custom price or give discount for a particular order',
      emptyState: {
        title: 'No modifiers found',
        description: 'No price modifiers included in this price set',
      },
    },
    packagesTab: {
      addPackage: 'Add Package',
      viewPackage: 'View Package',
      addPackageDesc: 'Fill the details of the add new package.',
      mustBeMoreThan1: 'Value should be more than 0',
      viewPackageDesc: 'Edit or view the package below',
      placeholderPackageType: 'Select package type',
      requiredPackageType: 'Please select package type',
      requiredQuantity: 'Quantity is required',
      requiredLength: 'Length is required',
      requiredWidth: 'Width is required',
      requiredHeight: 'Height is required',
      requiredWeight: 'Combined weight is required',
      FormLabels: {
        packageType: 'Package type',
        quantity: 'Quantity',
        CombinedWeight: 'Combined weight',
        length: 'Length',
        width: 'Width',
        height: 'Height',
        CubicDimension: 'Cubic dimension',
        image: 'Upload image',
      },
      colDefs: {
        packageType: 'Package Type',
        quantity: 'Quantity',
        CombinedWeight: 'Combined Weight',
        CubicDimension: 'Dimension',
        image: 'Image',
      },
      uploadImage: 'Upload image',
      chooseImage: 'Choose image',
      uploadButtonText: 'Upload',
    },
    attachmentsTab: {
      uploadFiles: 'Upload Files',
      uploadType: 'Upload type',
      uploadLabel: 'Upload file or image',
      requiredUploadType: 'Please select upload type',
      requiredAttachmentOrFile: 'Please choose attachment or file',
      selectPlaceholder: 'Select upload type',
      attachmentFile: 'Attachment File',
      type: 'Type',
      dateAdded: 'Date Added',
      uploadTypeOptions: {
        collectionSignature: 'Collection signature',
        deliverySignature: 'Delivery signature',
        fileAttachment: 'File attachment',
      },
      deleteConfirmationMessage:
        'This action cannot be undone, Deleting this Attachment will remove all their details from your records.',
      uploadAttachment: 'Upload File',
    },
  },
};

export default EN;
